# Test Plan: Offline Players Synchronization Fix

## Issue Description
After server restart, clients don't see offline players in town lists even though the data is saved correctly on the server.

## Root Cause
The online status of players was not being sent in the network packets from server to client, so clients couldn't distinguish between online and offline players.

## Fix Applied
1. **Modified `sendTownPlayerUpdate` method** in `TownDataSynchronizer.java`:
   - Added `buf.writeBoolean(townPlayer != null ? townPlayer.isOnline() : false);` to send online status
   
2. **Modified `handleTownPlayerUpdate` method** in `TownDataSynchronizer.java`:
   - Added `boolean isOnline = buf.readBoolean();` to read online status
   - Updated player creation/update to use the online status: `new TownPlayer(playerId, playerName, rank, isOnline)`
   
3. **Modified `TownPlayer.setOnline` method**:
   - Added data version increment when online status changes
   
4. **Modified `Town.addPlayer` method**:
   - Now always marks town as changed when player is updated (including online status changes)
   
5. **Modified `PlayerDataManager.onPlayerLeave` method**:
   - Added town update when player goes offline to trigger synchronization

## Test Steps

### Setup
1. Start the server
2. Have at least 2 players join
3. Create a test town: `/towntest create TestOfflineSync`
4. Have the second player join: `/towntest join TestOfflineSync`

### Test Scenario 1: Basic Online/Offline Status
1. Both players should see each other as online in the town list
2. Have Player 2 disconnect from the server
3. Player 1 should see Player 2 as offline in the town list
4. Have Player 2 reconnect
5. Player 1 should see Player 2 as online again

### Test Scenario 2: Server Restart Simulation
1. Have Player 2 disconnect (so they're offline)
2. Force save data: `/towntest save`
3. Force reload data: `/towntest reload` (simulates server restart)
4. Player 1 should still see Player 2 in the town list (as offline)
5. Have Player 2 reconnect
6. Player 1 should see Player 2 as online

### Test Scenario 3: Multiple Players
1. Have 3-4 players join the test town
2. Have 2 players disconnect
3. Force save and reload
4. Remaining online players should see all players (online and offline)

### Verification Commands
- `/synctest status` - Check synchronization status
- `/synctest force` - Force resynchronization
- `/towntest status` - Check town membership status
- `/clientsync status` - Check client-side sync status (client-side command)

## Expected Results
- Offline players should remain visible in town lists after server restart
- Online status should be correctly displayed (online vs offline)
- No data loss of player-town relationships
- Proper synchronization between server and all connected clients

## Files Modified
- `src/main/java/com/pokecobble/town/network/town/TownDataSynchronizer.java`
- `src/main/java/com/pokecobble/town/TownPlayer.java`
- `src/main/java/com/pokecobble/town/Town.java`
- `src/main/java/com/pokecobble/town/PlayerDataManager.java`
