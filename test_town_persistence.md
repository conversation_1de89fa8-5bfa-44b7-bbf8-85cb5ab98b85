# Town Membership Persistence Test Guide

This guide provides step-by-step instructions to manually test the town membership persistence fixes.

## Test Scenario: Verify Player Names Persist Across Server Restarts

### Prerequisites
- A Minecraft server with the pokecobbleclaim mod installed
- At least 2-3 players (can use multiple accounts or ask friends to help)
- Access to server console/logs

### Test Steps

#### Phase 1: Setup Town with Members
1. **Start the server** and have all test players join
2. **Create a town** using one player (this player becomes the owner):
   ```
   /town create TestPersistence
   ```
3. **Add other players to the town**:
   - Have the second player join: `/town join TestPersistence`
   - Or invite them: `/town invite <playername>`
4. **Promote some players** to different ranks:
   ```
   /town promote <playername> admin
   ```
5. **Verify the town roster** shows all players with correct names:
   ```
   /town info
   /town members
   ```
6. **Note down the player names and ranks** for comparison later

#### Phase 2: Simulate Mixed Online/Offline State
1. **Have some players leave the server** (but stay in the town)
2. **Keep at least one player online**
3. **Verify the town still shows all members** (both online and offline):
   ```
   /town members
   ```
4. **Check the MyTownScreen GUI** - it should show all players with their real names, not placeholder names

#### Phase 3: Server Restart Test
1. **Stop the server completely**
2. **Wait a few seconds** to ensure all data is saved
3. **Restart the server**
4. **Have the original online player reconnect**

#### Phase 4: Verification After Restart
1. **Check town membership immediately after restart**:
   ```
   /town info
   /town members
   ```
2. **Verify all players are still in the town** with correct names and ranks
3. **Open the MyTownScreen GUI** and verify:
   - All players are listed
   - Player names are correct (not "Unknown Player" or "Player-12345678")
   - Online/offline status is accurate
   - Ranks are preserved
4. **Have the offline players reconnect one by one** and verify:
   - They're still in the town
   - Their data is synchronized correctly
   - The GUI updates properly

### Expected Results

✅ **PASS Criteria:**
- All player names are preserved across server restart
- Player ranks are maintained correctly
- Online/offline status is accurate
- No "Unknown Player" or UUID-based placeholder names
- GUI shows complete member list with real names
- Players can reconnect and see their town membership intact

❌ **FAIL Indicators:**
- Player names show as "Unknown Player" after restart
- Players are missing from town after restart
- Placeholder names like "Player-12345678" appear
- Ranks are reset or incorrect
- GUI shows incomplete member list

### Troubleshooting

If tests fail, check:

1. **Server logs** for any errors during save/load:
   ```
   grep -i "town" server.log
   grep -i "player" server.log
   ```

2. **Data files** in the server directory:
   ```
   ls -la world/pokecobbleclaim/
   cat world/pokecobbleclaim/towns.json
   ```

3. **Player data files**:
   ```
   ls -la world/pokecobbleclaim/players/
   ```

### Additional Test Cases

#### Test Case 1: New Player Joins After Restart
1. After completing the main test, have a completely new player join the server
2. Invite them to the town
3. Verify they can see all existing members with correct names

#### Test Case 2: Multiple Towns
1. Create multiple towns with different players
2. Restart server
3. Verify all towns maintain their member lists correctly

#### Test Case 3: Player Name Changes
1. If possible, test with a player who changes their Minecraft username
2. Verify the system handles name updates correctly

### Performance Notes
- Monitor server startup time - it should not be significantly impacted
- Check memory usage - player name storage should have minimal impact
- Verify network traffic - client synchronization should be efficient

### Reporting Issues
If any tests fail, please report with:
- Exact steps that failed
- Server log excerpts
- Screenshots of GUI issues
- Player count and town configuration details
