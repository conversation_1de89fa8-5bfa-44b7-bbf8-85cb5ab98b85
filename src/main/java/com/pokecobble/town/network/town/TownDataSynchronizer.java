package com.pokecobble.town.network.town;

import com.pokecobble.Pokecobbleclaim;
import com.pokecobble.town.Town;
import com.pokecobble.town.TownManager;
import com.pokecobble.town.TownPlayer;
import com.pokecobble.town.TownPlayerRank;
import com.pokecobble.town.claim.ClaimTag;
import com.pokecobble.town.network.NetworkConstants;
import com.pokecobble.town.network.NetworkManager;
import com.pokecobble.town.network.PacketValidator;
import net.fabricmc.api.EnvType;
import net.fabricmc.api.Environment;
import net.fabricmc.fabric.api.networking.v1.PacketSender;
import net.minecraft.network.PacketByteBuf;
import net.minecraft.server.MinecraftServer;
import net.minecraft.server.network.ServerPlayerEntity;
import net.minecraft.util.Identifier;

import java.util.*;

/**
 * Handles synchronization of town data between server and clients.
 * This class is responsible for efficiently sending town updates to the appropriate players.
 */
public class TownDataSynchronizer {
    // Constants for town update packets
    public static final Identifier TOWN_UPDATE = new Identifier(NetworkConstants.MOD_ID, "town_update");
    public static final Identifier TOWN_PLAYER_UPDATE = new Identifier(NetworkConstants.MOD_ID, "town_player_update");
    public static final Identifier TOWN_TAG_UPDATE = new Identifier(NetworkConstants.MOD_ID, "town_tag_update");
    public static final Identifier TOWN_CLAIM_UPDATE = new Identifier(NetworkConstants.MOD_ID, "town_claim_update");
    public static final Identifier PLAYER_TOWN_MEMBERSHIP = new Identifier(NetworkConstants.MOD_ID, "player_town_membership");

    // Map to track the last known data version for each player
    private static final Map<UUID, Map<UUID, Integer>> playerTownVersions = new HashMap<>();

    // Callback interface for town player data updates
    public interface TownPlayerUpdateCallback {
        void onTownPlayerUpdate(UUID townId);
    }

    // Callback interface for town list updates
    public interface TownListUpdateCallback {
        void onTownListUpdate();
    }

    // List of registered callbacks
    private static final List<TownPlayerUpdateCallback> playerUpdateCallbacks = new ArrayList<>();
    private static final List<TownListUpdateCallback> townListUpdateCallbacks = new ArrayList<>();

    /**
     * Registers a callback to be notified when town player data is updated.
     *
     * @param callback The callback to register
     */
    public static void registerPlayerUpdateCallback(TownPlayerUpdateCallback callback) {
        playerUpdateCallbacks.add(callback);
    }

    /**
     * Unregisters a previously registered callback.
     *
     * @param callback The callback to unregister
     */
    public static void unregisterPlayerUpdateCallback(TownPlayerUpdateCallback callback) {
        playerUpdateCallbacks.remove(callback);
    }

    /**
     * Registers a callback to be notified when the town list is updated.
     *
     * @param callback The callback to register
     */
    public static void registerTownListUpdateCallback(TownListUpdateCallback callback) {
        townListUpdateCallbacks.add(callback);
    }

    /**
     * Unregisters a previously registered town list update callback.
     *
     * @param callback The callback to unregister
     */
    public static void unregisterTownListUpdateCallback(TownListUpdateCallback callback) {
        townListUpdateCallbacks.remove(callback);
    }

    /**
     * Notifies all registered callbacks that the town list has been updated.
     * This should be called whenever a town is created, removed, or its basic data changes.
     */
    public static void notifyTownListUpdated() {
        for (TownListUpdateCallback callback : townListUpdateCallbacks) {
            callback.onTownListUpdate();
        }
    }

    /**
     * Broadcasts the updated town list to all connected players.
     * This should be called whenever a town is created, removed, or its basic data changes.
     *
     * @param server The server instance
     */
    public static void broadcastTownListUpdate(MinecraftServer server) {
        if (server == null) {
            return;
        }

        try {
            // Get all towns
            Collection<Town> townCollection = TownManager.getInstance().getAllTowns();
            List<Town> towns = new ArrayList<>(townCollection);

            // Get all online players
            List<ServerPlayerEntity> onlinePlayers = server.getPlayerManager().getPlayerList();

            // Send updated town list to each player
            for (ServerPlayerEntity player : onlinePlayers) {
                sendTownListToPlayer(player, towns);
            }

            // Also notify callbacks
            notifyTownListUpdated();

            Pokecobbleclaim.LOGGER.info("Broadcasted town list update to " + onlinePlayers.size() + " players (" + towns.size() + " towns)");

        } catch (Exception e) {
            Pokecobbleclaim.LOGGER.error("Error broadcasting town list update: " + e.getMessage());
            e.printStackTrace();
        }
    }

    /**
     * Sends the town list to a specific player.
     *
     * @param player The player to send the list to
     * @param towns The list of towns to send
     */
    private static void sendTownListToPlayer(ServerPlayerEntity player, List<Town> towns) {
        try {
            // Create response packet
            PacketByteBuf buf = NetworkManager.createPacket();

            // Write town count
            buf.writeInt(towns.size());

            // Write town data
            for (Town town : towns) {
                // Write town UUID
                buf.writeUuid(town.getId());

                // Write town name
                buf.writeString(town.getName());

                // Write town description
                buf.writeString(town.getDescription());

                // Write town status
                buf.writeBoolean(town.isOpen());

                // Write player count (ensure it's current)
                buf.writeInt(town.getPlayerCount());

                // Write max players
                buf.writeInt(town.getMaxPlayers());

                // Write join type
                buf.writeString(town.getJoinType().name());
            }

            // Send packet to player
            NetworkManager.sendToPlayer(player, NetworkConstants.TOWN_LIST_RESPONSE, buf);

        } catch (Exception e) {
            Pokecobbleclaim.LOGGER.error("Error sending town list to player " + player.getName().getString() + ": " + e.getMessage());
        }
    }

    /**
     * Sends the current town list to a newly joined player.
     * This ensures new players see all existing towns immediately upon joining.
     *
     * @param player The player who just joined
     */
    public static void sendTownListToNewPlayer(ServerPlayerEntity player) {
        try {
            // Get all towns
            Collection<Town> townCollection = TownManager.getInstance().getAllTowns();
            List<Town> towns = new ArrayList<>(townCollection);

            // Send the town list to the player
            sendTownListToPlayer(player, towns);

            // Also send detailed player data for all towns to ensure the client has complete information
            // This is important after server restarts when player-town relationships need to be restored
            for (Town town : towns) {
                sendTownPlayerUpdate(player, town);
                sendTownTagUpdate(player, town);
                sendTownClaimUpdate(player, town);

                // Update the player's known version for this town
                updatePlayerTownVersion(player.getUuid(), town.getId(), town.getDataVersion());
            }

            // Send the player's town membership information to the client
            // This is crucial for the client to know which town they belong to
            UUID playerTownId = TownManager.getInstance().getPlayerTownId(player.getUuid());
            if (playerTownId != null) {
                Town playerTown = TownManager.getInstance().getTownById(playerTownId);
                if (playerTown != null) {
                    sendPlayerTownMembership(player, playerTownId, playerTown.getDataVersion());
                    Pokecobbleclaim.LOGGER.info("Sent player town membership to " + player.getName().getString() + " for town " + playerTown.getName());
                }
            }

            Pokecobbleclaim.LOGGER.info("Sent complete town data to new player " + player.getName().getString() + " (" + towns.size() + " towns)");

        } catch (Exception e) {
            Pokecobbleclaim.LOGGER.error("Error sending town data to new player " + player.getName().getString() + ": " + e.getMessage());
        }
    }

    /**
     * Registers client-side packet handlers for town data synchronization.
     */
    @Environment(EnvType.CLIENT)
    public static void registerClientHandlers() {
        // Register town update handler
        net.fabricmc.fabric.api.client.networking.v1.ClientPlayNetworking.registerGlobalReceiver(
                TOWN_UPDATE,
                TownDataSynchronizer::handleTownUpdate
        );

        // Register town player update handler
        net.fabricmc.fabric.api.client.networking.v1.ClientPlayNetworking.registerGlobalReceiver(
                TOWN_PLAYER_UPDATE,
                TownDataSynchronizer::handleTownPlayerUpdate
        );

        // Register town tag update handler
        net.fabricmc.fabric.api.client.networking.v1.ClientPlayNetworking.registerGlobalReceiver(
                TOWN_TAG_UPDATE,
                TownDataSynchronizer::handleTownTagUpdate
        );

        // Register town claim update handler
        net.fabricmc.fabric.api.client.networking.v1.ClientPlayNetworking.registerGlobalReceiver(
                TOWN_CLAIM_UPDATE,
                TownDataSynchronizer::handleTownClaimUpdate
        );

        // Register player town membership handler
        net.fabricmc.fabric.api.client.networking.v1.ClientPlayNetworking.registerGlobalReceiver(
                PLAYER_TOWN_MEMBERSHIP,
                TownDataSynchronizer::handlePlayerTownMembership
        );
    }

    /**
     * Registers server-side packet handlers for town data synchronization.
     */
    public static void registerServerHandlers() {
        // No server-side handlers needed for these packets
        // These are server->client only
    }

    /**
     * Synchronizes town data to all relevant players.
     * This method efficiently sends only the changed data to the players who need it.
     *
     * @param server The server instance
     * @param town The town to synchronize
     */
    public static void syncTownData(MinecraftServer server, Town town) {
        if (town == null) {
            return;
        }

        // Get all changed aspects
        Set<String> changedAspects = town.getChangedAspects();

        // If nothing has changed, no need to sync
        if (changedAspects.isEmpty()) {
            return;
        }

        // Get all online players
        List<ServerPlayerEntity> onlinePlayers = server.getPlayerManager().getPlayerList();

        // Determine which players need which updates
        for (ServerPlayerEntity player : onlinePlayers) {
            UUID playerId = player.getUuid();

            // Check if this player needs an update for this town
            if (shouldSendTownUpdate(playerId, town)) {
                // Send appropriate updates based on changed aspects
                sendTownUpdates(player, town, changedAspects);

                // Update the player's known version for this town
                updatePlayerTownVersion(playerId, town.getId(), town.getDataVersion());
            }
        }

        // Clear changed aspects after sync
        town.clearChangedAspects();
    }

    /**
     * Determines if a town update should be sent to a player.
     *
     * @param playerId The player's UUID
     * @param town The town
     * @return True if the player should receive an update, false otherwise
     */
    private static boolean shouldSendTownUpdate(UUID playerId, Town town) {
        // Players in the town always get updates
        if (town.getPlayers().contains(playerId)) {
            return true;
        }

        // Players who have claimed chunks near this town's claims should get updates
        // This would require a spatial lookup system - for now, we'll just check if the player
        // is in a town that has claims near this town's claims

        // For simplicity in this implementation, we'll just send updates to all players
        // In a real implementation, you would want to be more selective
        return true;
    }

    /**
     * Updates the player's known version for a town.
     *
     * @param playerId The player's UUID
     * @param townId The town's UUID
     * @param version The new version
     */
    private static void updatePlayerTownVersion(UUID playerId, UUID townId, int version) {
        Map<UUID, Integer> townVersions = playerTownVersions.computeIfAbsent(playerId, k -> new HashMap<>());
        townVersions.put(townId, version);
    }

    /**
     * Gets the player's known version for a town.
     *
     * @param playerId The player's UUID
     * @param townId The town's UUID
     * @return The known version, or 0 if not known
     */
    private static int getPlayerTownVersion(UUID playerId, UUID townId) {
        Map<UUID, Integer> townVersions = playerTownVersions.get(playerId);
        if (townVersions == null) {
            return 0;
        }
        return townVersions.getOrDefault(townId, 0);
    }

    /**
     * Sends appropriate town updates to a player based on changed aspects.
     *
     * @param player The player to send updates to
     * @param town The town
     * @param changedAspects The aspects that have changed
     */
    private static void sendTownUpdates(ServerPlayerEntity player, Town town, Set<String> changedAspects) {
        // Check if basic town data has changed
        if (hasBasicDataChanged(changedAspects)) {
            sendBasicTownUpdate(player, town);
        }

        // Check if player data has changed
        if (changedAspects.contains(Town.ASPECT_PLAYERS) || changedAspects.contains(Town.ASPECT_RANKS) ||
            changedAspects.contains(Town.ASPECT_PERMISSIONS)) {
            sendTownPlayerUpdate(player, town);
        }

        // Check if tag data has changed
        if (changedAspects.contains(Town.ASPECT_TAGS)) {
            sendTownTagUpdate(player, town);
        }

        // Check if claim data has changed
        if (changedAspects.contains(Town.ASPECT_CLAIMS)) {
            sendTownClaimUpdate(player, town);
        }

        // Check if image data has changed
        if (changedAspects.contains(Town.ASPECT_IMAGE)) {
            // Use the TownImageSynchronizer to handle image updates
            com.pokecobble.town.network.town.TownImageSynchronizer.syncTownImageData(
                player.getServer(), town);
        }
    }

    /**
     * Checks if basic town data has changed.
     *
     * @param changedAspects The aspects that have changed
     * @return True if basic data has changed, false otherwise
     */
    private static boolean hasBasicDataChanged(Set<String> changedAspects) {
        return changedAspects.contains(Town.ASPECT_NAME) ||
               changedAspects.contains(Town.ASPECT_DESCRIPTION) ||
               changedAspects.contains(Town.ASPECT_OPEN) ||
               changedAspects.contains(Town.ASPECT_MAX_PLAYERS) ||
               changedAspects.contains(Town.ASPECT_ELECTION) ||
               changedAspects.contains(Town.ASPECT_IMAGE);
    }

    /**
     * Sends basic town data to a player.
     *
     * @param player The player to send data to
     * @param town The town
     */
    private static void sendBasicTownUpdate(ServerPlayerEntity player, Town town) {
        try {
            // Create packet buffer
            PacketByteBuf buf = NetworkManager.createPacket();

            // Write town UUID
            buf.writeUuid(town.getId());

            // Write town data version
            buf.writeInt(town.getDataVersion());

            // Write town name
            buf.writeString(town.getName());

            // Write town description
            buf.writeString(town.getDescription());

            // Write town status
            buf.writeBoolean(town.isOpen());

            // Write player count
            buf.writeInt(town.getPlayerCount());

            // Write max players
            buf.writeInt(town.getMaxPlayers());

            // Write election status
            buf.writeBoolean(town.isInElection());

            // Write town image name
            buf.writeString(town.getImage() != null ? town.getImage() : "default", NetworkConstants.MAX_STRING_LENGTH);

            // Send packet to player
            NetworkManager.sendToPlayer(player, TOWN_UPDATE, buf);

            Pokecobbleclaim.LOGGER.debug("Sent basic town update for " + town.getName() + " to " + player.getName().getString());
        } catch (Exception e) {
            Pokecobbleclaim.LOGGER.error("Error sending basic town update: " + e.getMessage());
        }
    }

    /**
     * Sends town player data to a player.
     *
     * @param player The player to send data to
     * @param town The town
     */
    private static void sendTownPlayerUpdate(ServerPlayerEntity player, Town town) {
        try {
            // Create packet buffer
            PacketByteBuf buf = NetworkManager.createPacket();

            // Write town UUID
            buf.writeUuid(town.getId());

            // Write town data version
            buf.writeInt(town.getDataVersion());

            // Write player count
            List<UUID> townPlayers = town.getPlayers();
            buf.writeInt(townPlayers.size());

            // Write player data
            for (UUID playerId : townPlayers) {
                // Write player UUID
                buf.writeUuid(playerId);

                // Get player data
                TownPlayer townPlayer = town.getPlayer(playerId);

                // Write player name
                buf.writeString(townPlayer != null ? townPlayer.getName() : "Unknown");

                // Write player rank
                TownPlayerRank rank = town.getPlayerRank(playerId);
                buf.writeInt(rank != null ? rank.ordinal() : TownPlayerRank.MEMBER.ordinal());

                // Write player online status
                buf.writeBoolean(townPlayer != null ? townPlayer.isOnline() : false);

                // Write player data version
                buf.writeInt(townPlayer != null ? townPlayer.getDataVersion() : 0);

                // Write player permissions (only if this is the player we're sending to or they're an admin)
                boolean sendPermissions = player.getUuid().equals(playerId) ||
                                         town.getPlayerRank(player.getUuid()) == TownPlayerRank.OWNER ||
                                         town.getPlayerRank(player.getUuid()) == TownPlayerRank.ADMIN;

                if (sendPermissions && townPlayer != null) {
                    // Write permission flag
                    buf.writeBoolean(true);

                    // Write permissions
                    Map<String, Map<String, Boolean>> permissions = townPlayer.getAllPermissions();

                    // Write category count
                    buf.writeInt(permissions.size());

                    // Write categories
                    for (Map.Entry<String, Map<String, Boolean>> entry : permissions.entrySet()) {
                        // Write category name
                        buf.writeString(entry.getKey());

                        // Write permission count
                        Map<String, Boolean> categoryPermissions = entry.getValue();
                        buf.writeInt(categoryPermissions.size());

                        // Write permissions
                        for (Map.Entry<String, Boolean> permEntry : categoryPermissions.entrySet()) {
                            // Write permission name
                            buf.writeString(permEntry.getKey());

                            // Write permission value
                            buf.writeBoolean(permEntry.getValue());
                        }
                    }
                } else {
                    // Write permission flag
                    buf.writeBoolean(false);
                }
            }

            // Send packet to player
            NetworkManager.sendToPlayer(player, TOWN_PLAYER_UPDATE, buf);

            Pokecobbleclaim.LOGGER.debug("Sent town player update for " + town.getName() + " to " + player.getName().getString());
        } catch (Exception e) {
            Pokecobbleclaim.LOGGER.error("Error sending town player update: " + e.getMessage());
        }
    }

    /**
     * Sends player town membership information to a player.
     * This tells the client which town the player belongs to.
     *
     * @param player The player to send data to
     * @param townId The ID of the town the player belongs to
     * @param townVersion The version of the town data
     */
    private static void sendPlayerTownMembership(ServerPlayerEntity player, UUID townId, int townVersion) {
        try {
            // Create packet buffer
            PacketByteBuf buf = NetworkManager.createPacket();

            // Write town UUID
            buf.writeUuid(townId);

            // Write town version
            buf.writeInt(townVersion);

            // Send packet to player
            NetworkManager.sendToPlayer(player, PLAYER_TOWN_MEMBERSHIP, buf);

            Pokecobbleclaim.LOGGER.debug("Sent player town membership for " + townId + " to " + player.getName().getString());
        } catch (Exception e) {
            Pokecobbleclaim.LOGGER.error("Error sending player town membership: " + e.getMessage());
        }
    }

    /**
     * Sends town tag data to a player.
     *
     * @param player The player to send data to
     * @param town The town
     */
    private static void sendTownTagUpdate(ServerPlayerEntity player, Town town) {
        try {
            // Create packet buffer
            PacketByteBuf buf = NetworkManager.createPacket();

            // Write town UUID
            buf.writeUuid(town.getId());

            // Write town data version
            buf.writeInt(town.getDataVersion());

            // Write tag count
            List<ClaimTag> tags = town.getClaimTags();
            buf.writeInt(tags.size());

            // Write tag data
            for (ClaimTag tag : tags) {
                // Write tag ID as String
                buf.writeString(tag.getId().toString());

                // Write tag name
                buf.writeString(tag.getName());

                // Write tag color
                buf.writeInt(tag.getColor());

                // Write tag permissions
                // This would depend on the implementation of ClaimTag
                // For now, we'll just write a placeholder
                buf.writeInt(0); // Placeholder for permission count
            }

            // Send packet to player
            NetworkManager.sendToPlayer(player, TOWN_TAG_UPDATE, buf);

            Pokecobbleclaim.LOGGER.debug("Sent town tag update for " + town.getName() + " to " + player.getName().getString());
        } catch (Exception e) {
            Pokecobbleclaim.LOGGER.error("Error sending town tag update: " + e.getMessage());
        }
    }

    /**
     * Sends town claim data to a player.
     *
     * @param player The player to send data to
     * @param town The town
     */
    private static void sendTownClaimUpdate(ServerPlayerEntity player, Town town) {
        try {
            // Create packet buffer
            PacketByteBuf buf = NetworkManager.createPacket();

            // Write town UUID
            buf.writeUuid(town.getId());

            // Write town data version
            buf.writeInt(town.getDataVersion());

            // Write claim count
            Collection<net.minecraft.util.math.ChunkPos> claims = town.getClaimedChunks();
            buf.writeInt(claims.size());

            // Write claim data
            for (net.minecraft.util.math.ChunkPos claim : claims) {
                // Write chunk X
                buf.writeInt(claim.x);

                // Write chunk Z
                buf.writeInt(claim.z);

                // Write tag ID as String
                ClaimTag tag = town.getChunkTag(claim);
                buf.writeString(tag != null ? tag.getId().toString() : "");
            }

            // Send packet to player
            NetworkManager.sendToPlayer(player, TOWN_CLAIM_UPDATE, buf);

            Pokecobbleclaim.LOGGER.debug("Sent town claim update for " + town.getName() + " to " + player.getName().getString());
        } catch (Exception e) {
            Pokecobbleclaim.LOGGER.error("Error sending town claim update: " + e.getMessage());
        }
    }

    /**
     * Handles town update packets on the client side.
     */
    @Environment(EnvType.CLIENT)
    private static void handleTownUpdate(net.minecraft.client.MinecraftClient client,
                                        net.minecraft.client.network.ClientPlayNetworkHandler handler,
                                        PacketByteBuf buf,
                                        PacketSender sender) {
        try {
            // Read town UUID
            UUID townId = buf.readUuid();

            // Read town data version
            int dataVersion = buf.readInt();

            // Read town name
            String name = buf.readString(NetworkConstants.MAX_STRING_LENGTH);

            // Read town description
            String description = buf.readString(NetworkConstants.MAX_STRING_LENGTH);

            // Read town status
            boolean isOpen = buf.readBoolean();

            // Read player count
            int playerCount = buf.readInt();

            // Read max players
            int maxPlayers = buf.readInt();

            // Read election status
            boolean inElection = buf.readBoolean();

            // Read town image name
            String imageName = buf.readString(NetworkConstants.MAX_STRING_LENGTH);

            // Get or create town
            Town town = TownManager.getInstance().getTownById(townId);
            if (town == null) {
                town = new Town(townId, name);
                TownManager.getInstance().addTown(town);
            }

            // Update town data
            town.setName(name);
            town.setDescription(description);
            town.setOpen(isOpen);
            town.setMaxPlayers(maxPlayers);
            town.setInElection(inElection);

            // Update town image
            if (imageName != null && !imageName.isEmpty()) {
                town.setImage(imageName);
            }

            // Set data version
            town.setDataVersion(dataVersion);

            // Clear changed aspects since we just updated
            town.clearChangedAspects();

            Pokecobbleclaim.LOGGER.debug("Received basic town update for " + name);

            // Notify town list update callbacks
            notifyTownListUpdated();
        } catch (Exception e) {
            Pokecobbleclaim.LOGGER.error("Error handling town update: " + e.getMessage());
        }
    }

    /**
     * Handles town player update packets on the client side.
     */
    @Environment(EnvType.CLIENT)
    private static void handleTownPlayerUpdate(net.minecraft.client.MinecraftClient client,
                                              net.minecraft.client.network.ClientPlayNetworkHandler handler,
                                              PacketByteBuf buf,
                                              PacketSender sender) {
        try {
            // Read town UUID
            UUID townId = buf.readUuid();

            // Read town data version
            int dataVersion = buf.readInt();

            // Get town from client cache first, then server manager
            Town town = com.pokecobble.town.client.ClientTownManager.getInstance().getTown(townId);
            if (town == null) {
                town = TownManager.getInstance().getTownById(townId);
            }
            if (town == null) {
                Pokecobbleclaim.LOGGER.warn("Received player update for unknown town: " + townId);
                return;
            }

            // Read player count
            int playerCount = buf.readInt();

            // Create a set of current players to track removals
            Set<UUID> currentPlayers = new HashSet<>(town.getPlayers());

            // Read player data
            for (int i = 0; i < playerCount; i++) {
                // Read player UUID
                UUID playerId = buf.readUuid();

                // Read player name
                String playerName = buf.readString(NetworkConstants.MAX_STRING_LENGTH);

                // Read player rank
                int rankOrdinal = buf.readInt();
                TownPlayerRank rank = TownPlayerRank.values()[rankOrdinal];

                // Read player online status
                boolean isOnline = buf.readBoolean();

                // Read player data version
                int playerDataVersion = buf.readInt();

                // Remove from current players set (to track which ones are left)
                currentPlayers.remove(playerId);

                // Get or create player
                TownPlayer townPlayer = town.getPlayer(playerId);
                if (townPlayer == null) {
                    townPlayer = new TownPlayer(playerId, playerName, rank, isOnline);
                    town.addPlayer(townPlayer);
                } else {
                    // Update player data
                    townPlayer.setName(playerName);
                    townPlayer.setRank(rank);
                    townPlayer.setOnline(isOnline);
                }

                // Set data version
                townPlayer.setDataVersion(playerDataVersion);

                // Read permissions flag
                boolean hasPermissions = buf.readBoolean();

                if (hasPermissions) {
                    // Read category count
                    int categoryCount = buf.readInt();

                    // Read categories
                    for (int j = 0; j < categoryCount; j++) {
                        // Read category name
                        String category = buf.readString(NetworkConstants.MAX_STRING_LENGTH);

                        // Read permission count
                        int permissionCount = buf.readInt();

                        // Create permission map
                        Map<String, Boolean> permissions = new HashMap<>();

                        // Read permissions
                        for (int k = 0; k < permissionCount; k++) {
                            // Read permission name
                            String permission = buf.readString(NetworkConstants.MAX_STRING_LENGTH);

                            // Read permission value
                            boolean value = buf.readBoolean();

                            // Add to map
                            permissions.put(permission, value);
                        }

                        // Set permissions
                        townPlayer.setCategoryPermissions(category, permissions);
                    }
                }
            }

            // Remove players that weren't in the update
            for (UUID playerId : currentPlayers) {
                town.removePlayer(playerId);
            }

            // Set data version
            town.setDataVersion(dataVersion);

            // Clear changed aspects since we just updated
            town.clearChangedAspects();

            // Update client cache
            com.pokecobble.town.client.ClientTownManager.getInstance().updateTown(town, dataVersion);

            // Fire sync event
            com.pokecobble.town.client.ClientSyncManager.getInstance().onTownUpdated(townId, dataVersion);

            Pokecobbleclaim.LOGGER.debug("Received town player update for " + town.getName() + " with " + playerCount + " players");

            // Notify callbacks about the player update
            for (TownPlayerUpdateCallback callback : playerUpdateCallbacks) {
                callback.onTownPlayerUpdate(townId);
            }
        } catch (Exception e) {
            Pokecobbleclaim.LOGGER.error("Error handling town player update: " + e.getMessage());
        }
    }

    /**
     * Handles town tag update packets on the client side.
     */
    @Environment(EnvType.CLIENT)
    private static void handleTownTagUpdate(net.minecraft.client.MinecraftClient client,
                                           net.minecraft.client.network.ClientPlayNetworkHandler handler,
                                           PacketByteBuf buf,
                                           PacketSender sender) {
        try {
            // Read town UUID
            UUID townId = buf.readUuid();

            // Read town data version
            int dataVersion = buf.readInt();

            // Get town from client cache first, then server manager
            Town town = com.pokecobble.town.client.ClientTownManager.getInstance().getTown(townId);
            if (town == null) {
                town = TownManager.getInstance().getTownById(townId);
            }
            if (town == null) {
                Pokecobbleclaim.LOGGER.warn("Received tag update for unknown town: " + townId);
                return;
            }

            // Read tag count
            int tagCount = buf.readInt();

            // Create new tag list
            List<ClaimTag> tags = new ArrayList<>();

            // Read tag data
            for (int i = 0; i < tagCount; i++) {
                // Read tag ID
                String tagId = buf.readString(NetworkConstants.MAX_STRING_LENGTH);

                // Read tag name
                String tagName = buf.readString(NetworkConstants.MAX_STRING_LENGTH);

                // Read tag color
                int tagColor = buf.readInt();

                // Create tag
                ClaimTag tag = new ClaimTag(tagName, tagColor);

                // Read permission count (placeholder)
                int permissionCount = buf.readInt();

                // Add to list
                tags.add(tag);
            }

            // Update town tags
            town.updateClaimTags(tags);

            // Set data version
            town.setDataVersion(dataVersion);

            // Clear changed aspects since we just updated
            town.clearChangedAspects();

            // Update client cache
            com.pokecobble.town.client.ClientTownManager.getInstance().updateTown(town, dataVersion);

            // Fire sync event
            com.pokecobble.town.client.ClientSyncManager.getInstance().onTownUpdated(townId, dataVersion);

            Pokecobbleclaim.LOGGER.debug("Received town tag update for " + town.getName() + " with " + tagCount + " tags");
        } catch (Exception e) {
            Pokecobbleclaim.LOGGER.error("Error handling town tag update: " + e.getMessage());
        }
    }

    /**
     * Handles town claim update packets on the client side.
     */
    @Environment(EnvType.CLIENT)
    private static void handleTownClaimUpdate(net.minecraft.client.MinecraftClient client,
                                             net.minecraft.client.network.ClientPlayNetworkHandler handler,
                                             PacketByteBuf buf,
                                             PacketSender sender) {
        try {
            // Read town UUID
            UUID townId = buf.readUuid();

            // Read town data version
            int dataVersion = buf.readInt();

            // Get town from client cache first, then server manager
            Town town = com.pokecobble.town.client.ClientTownManager.getInstance().getTown(townId);
            if (town == null) {
                town = TownManager.getInstance().getTownById(townId);
            }
            if (town == null) {
                Pokecobbleclaim.LOGGER.warn("Received claim update for unknown town: " + townId);
                return;
            }

            // Read claim count
            int claimCount = buf.readInt();

            // Update claim count
            town.setClaimCount(claimCount);

            // Read claim data
            // In a real implementation, you would update the town's claimed chunks
            // For now, we'll just read the data
            for (int i = 0; i < claimCount; i++) {
                // Read chunk X
                int chunkX = buf.readInt();

                // Read chunk Z
                int chunkZ = buf.readInt();

                // Read tag ID
                String tagId = buf.readString(NetworkConstants.MAX_STRING_LENGTH);

                // In a real implementation, you would update the town's claimed chunks
                // For now, we'll just log it
                Pokecobbleclaim.LOGGER.debug("Chunk " + chunkX + "," + chunkZ + " claimed by " + town.getName() + " with tag " + tagId);
            }

            // Set data version
            town.setDataVersion(dataVersion);

            // Clear changed aspects since we just updated
            town.clearChangedAspects();

            // Update client cache
            com.pokecobble.town.client.ClientTownManager.getInstance().updateTown(town, dataVersion);

            // Fire sync event
            com.pokecobble.town.client.ClientSyncManager.getInstance().onTownUpdated(townId, dataVersion);

            Pokecobbleclaim.LOGGER.debug("Received town claim update for " + town.getName() + " with " + claimCount + " claims");
        } catch (Exception e) {
            Pokecobbleclaim.LOGGER.error("Error handling town claim update: " + e.getMessage());
        }
    }

    /**
     * Handles player town membership packets on the client side.
     */
    @Environment(EnvType.CLIENT)
    private static void handlePlayerTownMembership(net.minecraft.client.MinecraftClient client,
                                                  net.minecraft.client.network.ClientPlayNetworkHandler handler,
                                                  PacketByteBuf buf,
                                                  PacketSender sender) {
        try {
            // Read town UUID
            UUID townId = buf.readUuid();

            // Read town version
            int townVersion = buf.readInt();

            // Set the player's town in the client manager
            com.pokecobble.town.client.ClientTownManager.getInstance().setPlayerTown(townId, townVersion);

            Pokecobbleclaim.LOGGER.info("Received player town membership: " + townId + " (version " + townVersion + ")");

        } catch (Exception e) {
            Pokecobbleclaim.LOGGER.error("Error handling player town membership: " + e.getMessage());
        }
    }

    /**
     * Resets all player town versions.
     * This should be called when the server starts.
     */
    public static void resetAllPlayerTownVersions() {
        playerTownVersions.clear();
    }

    /**
     * Removes a player from the version tracking.
     * This should be called when a player disconnects.
     *
     * @param playerId The player's UUID
     */
    public static void removePlayer(UUID playerId) {
        playerTownVersions.remove(playerId);
    }

    /**
     * Synchronizes town data for a specific player.
     * This method sends all relevant town data to a specific player.
     *
     * @param server The server instance
     * @param playerId The player's UUID
     */
    public static void syncPlayerTownData(MinecraftServer server, UUID playerId) {
        if (server == null || playerId == null) {
            return;
        }

        // Get the player
        ServerPlayerEntity player = server.getPlayerManager().getPlayer(playerId);
        if (player == null) {
            return; // Player is not online
        }

        // Get the player's town
        Town playerTown = TownManager.getInstance().getPlayerTown(playerId);
        if (playerTown != null) {
            // Send all town data to the player
            sendBasicTownUpdate(player, playerTown);
            sendTownPlayerUpdate(player, playerTown);
            sendTownTagUpdate(player, playerTown);
            sendTownClaimUpdate(player, playerTown);

            // Update the player's known version for this town
            updatePlayerTownVersion(playerId, playerTown.getId(), playerTown.getDataVersion());
        }

        // Also send data for any other towns the player might need to know about
        // (e.g., neighboring towns, towns they have permissions in, etc.)
        for (Town town : TownManager.getInstance().getAllTowns()) {
            if (town != playerTown && shouldSendTownUpdate(playerId, town)) {
                sendBasicTownUpdate(player, town);
                sendTownPlayerUpdate(player, town);
                sendTownTagUpdate(player, town);
                sendTownClaimUpdate(player, town);

                // Update the player's known version for this town
                updatePlayerTownVersion(playerId, town.getId(), town.getDataVersion());
            }
        }
    }
}
