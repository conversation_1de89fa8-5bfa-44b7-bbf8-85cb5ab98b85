package com.pokecobble.town;

import java.util.HashMap;
import java.util.Map;
import java.util.UUID;

/**
 * Represents a player in a town.
 */
public class TownPlayer {
    private final UUID uuid;
    private String name;
    private TownPlayerRank rank;
    private boolean online;

    // Store permissions by category
    private final Map<String, Map<String, Boolean>> permissions = new HashMap<>();

    // Version tracking for data synchronization
    private int dataVersion = 0;

    /**
     * Creates a new town player.
     *
     * @param uuid The player's UUID
     * @param name The player's name
     * @param rank The player's rank
     * @param online Whether the player is online
     */
    public TownPlayer(UUID uuid, String name, TownPlayerRank rank, boolean online) {
        this.uuid = uuid;
        this.name = name;
        this.rank = rank;
        this.online = online;

        // Initialize default permissions based on rank
        initializeDefaultPermissions();
    }

    /**
     * Creates a new town player.
     *
     * @param uuid The player's UUID
     * @param name The player's name
     * @param rank The player's rank
     */
    public TownPlayer(UUID uuid, String name, TownPlayerRank rank) {
        this(uuid, name, rank, false);
    }

    /**
     * Initializes default permissions based on the player's rank.
     */
    private void initializeDefaultPermissions() {
        // Player Management permissions
        Map<String, Boolean> playerManageToggles = new HashMap<>();
        playerManageToggles.put("Can kick players", rank.ordinal() <= TownPlayerRank.ADMIN.ordinal());
        playerManageToggles.put("Can change player ranks", rank.ordinal() <= TownPlayerRank.ADMIN.ordinal());
        playerManageToggles.put("Can manage player permissions", rank.ordinal() <= TownPlayerRank.ADMIN.ordinal());
        playerManageToggles.put("Can invite players", rank.ordinal() <= TownPlayerRank.OWNER.ordinal());
        permissions.put("Player Management", playerManageToggles);

        // Claim Tool permissions
        Map<String, Boolean> claimToggles = new HashMap<>();
        claimToggles.put("Can view claims", true); // Everyone can view claims

        // Only the owner gets access to the claim tool by default
        boolean isOwner = (rank == TownPlayerRank.OWNER);
        claimToggles.put("Can access claim tool", isOwner); // Only enabled for owner by default
        claimToggles.put("Can delete claims", isOwner); // Only enabled for owner by default
        permissions.put("Claim Tool", claimToggles);

        // Town Bank permissions
        Map<String, Boolean> bankToggles = new HashMap<>();
        bankToggles.put("Can view balance", rank.ordinal() <= TownPlayerRank.MEMBER.ordinal());
        bankToggles.put("Can deposit money", rank.ordinal() <= TownPlayerRank.MEMBER.ordinal());
        bankToggles.put("Can withdraw money", rank.ordinal() <= TownPlayerRank.ADMIN.ordinal());
        bankToggles.put("Can view transactions", rank.ordinal() <= TownPlayerRank.ADMIN.ordinal());
        permissions.put("Town Bank", bankToggles);

        // Town Level permissions
        Map<String, Boolean> levelToggles = new HashMap<>();
        levelToggles.put("Can view level", rank.ordinal() <= TownPlayerRank.MEMBER.ordinal());
        levelToggles.put("Can contribute resources", rank.ordinal() <= TownPlayerRank.MEMBER.ordinal());
        levelToggles.put("Can view benefits", rank.ordinal() <= TownPlayerRank.MEMBER.ordinal());
        permissions.put("Town Level", levelToggles);

        // Town Settings permissions
        Map<String, Boolean> settingsToggles = new HashMap<>();
        settingsToggles.put("Can view settings", rank.ordinal() <= TownPlayerRank.MEMBER.ordinal());
        settingsToggles.put("Can modify settings", rank.ordinal() <= TownPlayerRank.ADMIN.ordinal());
        settingsToggles.put("Can change town name", rank.ordinal() <= TownPlayerRank.OWNER.ordinal());
        settingsToggles.put("Can change town description", rank.ordinal() <= TownPlayerRank.ADMIN.ordinal());
        settingsToggles.put("Can change town spawn", rank.ordinal() <= TownPlayerRank.ADMIN.ordinal());
        permissions.put("Town Settings", settingsToggles);
    }

    /**
     * Gets the player's UUID.
     *
     * @return The player's UUID
     */
    public UUID getUuid() {
        return uuid;
    }

    /**
     * Gets the player's name.
     *
     * @return The player's name
     */
    public String getName() {
        return name;
    }

    /**
     * Sets the player's name.
     *
     * @param name The new name
     */
    public void setName(String name) {
        this.name = name;
    }

    /**
     * Gets the player's rank.
     *
     * @return The player's rank
     */
    public TownPlayerRank getRank() {
        return rank;
    }

    /**
     * Sets the player's rank.
     *
     * @param rank The new rank
     */
    public void setRank(TownPlayerRank rank) {
        TownPlayerRank oldRank = this.rank;
        this.rank = rank;

        // Increment data version since data has changed
        incrementDataVersion();

        // Trigger rank change synchronization if server is available
        try {
            net.minecraft.server.MinecraftServer server = com.pokecobble.Pokecobbleclaim.getServer();
            if (server != null) {
                com.pokecobble.town.permission.PermissionChangeTracker.getInstance()
                    .trackPlayerRankChange(server, this.uuid, oldRank, rank);
            }
        } catch (Exception e) {
            com.pokecobble.Pokecobbleclaim.LOGGER.error("Error triggering rank change sync: " + e.getMessage());
        }
    }

    /**
     * Checks if the player is online.
     *
     * @return True if the player is online, false otherwise
     */
    public boolean isOnline() {
        return online;
    }

    /**
     * Sets whether the player is online.
     *
     * @param online True if the player is online, false otherwise
     */
    public void setOnline(boolean online) {
        if (this.online != online) {
            this.online = online;

            // Increment data version since data has changed
            incrementDataVersion();
        }
    }

    /**
     * Gets all permissions for a specific category.
     *
     * @param category The category name
     * @return A map of permission names to boolean values
     */
    public Map<String, Boolean> getCategoryPermissions(String category) {
        return permissions.getOrDefault(category, new HashMap<>());
    }

    /**
     * Sets all permissions for a specific category.
     *
     * @param category The category name
     * @param categoryPermissions The new permissions
     */
    public void setCategoryPermissions(String category, Map<String, Boolean> categoryPermissions) {
        permissions.put(category, new HashMap<>(categoryPermissions));

        // Increment data version since data has changed
        incrementDataVersion();
    }

    /**
     * Gets a specific permission value.
     *
     * @param category The category name
     * @param permission The permission name
     * @return The permission value, or false if not set
     */
    public boolean hasPermission(String category, String permission) {
        Map<String, Boolean> categoryPermissions = permissions.get(category);
        if (categoryPermissions == null) {
            return false;
        }
        return categoryPermissions.getOrDefault(permission, false);
    }

    /**
     * Sets a specific permission value.
     *
     * @param category The category name
     * @param permission The permission name
     * @param value The new value
     */
    public void setPermission(String category, String permission, boolean value) {
        Map<String, Boolean> categoryPermissions = permissions.computeIfAbsent(category, k -> new HashMap<>());
        categoryPermissions.put(permission, value);

        // Increment data version since data has changed
        incrementDataVersion();

        // Trigger permission synchronization if server is available
        try {
            net.minecraft.server.MinecraftServer server = com.pokecobble.Pokecobbleclaim.getServer();
            if (server != null) {
                com.pokecobble.town.permission.PermissionChangeTracker.getInstance()
                    .trackPlayerPermissionChange(server, this.uuid, category, permission, value);
            }
        } catch (Exception e) {
            com.pokecobble.Pokecobbleclaim.LOGGER.error("Error triggering permission sync: " + e.getMessage());
        }
    }

    /**
     * Gets all permissions for all categories.
     *
     * @return A map of category names to permission maps
     */
    public Map<String, Map<String, Boolean>> getAllPermissions() {
        // Return a deep copy to prevent modification
        Map<String, Map<String, Boolean>> result = new HashMap<>();
        for (Map.Entry<String, Map<String, Boolean>> entry : permissions.entrySet()) {
            result.put(entry.getKey(), new HashMap<>(entry.getValue()));
        }
        return result;
    }

    /**
     * Gets the current data version of this player.
     *
     * @return The data version
     */
    public int getDataVersion() {
        return dataVersion;
    }

    /**
     * Sets the data version of this player.
     *
     * @param dataVersion The new data version
     */
    public void setDataVersion(int dataVersion) {
        this.dataVersion = dataVersion;
    }

    /**
     * Increments the data version of this player.
     *
     * @return The new data version
     */
    public int incrementDataVersion() {
        return ++dataVersion;
    }
}
